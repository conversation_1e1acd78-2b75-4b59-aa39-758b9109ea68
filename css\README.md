# Art Nouveau CSS 架构说明

## 📁 文件结构

```
css/
├── main.css              # 主入口文件，导入所有模块
├── base.css              # 基础样式：重置、字体、光标
├── layout.css            # 布局系统：滚动、容器、响应式
├── components.css        # 通用组件：标题、按钮、装饰
├── navigation.css        # 导航组件：进度条、项目导航、联系方式
├── animations.css        # 动画效果：过渡、关键帧、交互
├── pages/               # 页面专用样式
│   ├── home.css         # 首页样式
│   ├── about.css        # 关于页面
│   ├── portfolio.css    # 作品集页面
│   ├── contact.css      # 联系页面
│   └── projects.css     # 项目展示页面
└── README.md            # 本说明文件
```

## 🎯 设计原则

### 1. **模块化分离**
- 每个文件负责特定功能
- 避免样式冲突和重复
- 便于维护和扩展

### 2. **层次化组织**
- **基础层**: 重置样式、全局设置
- **布局层**: 页面结构、响应式系统
- **组件层**: 可复用的UI组件
- **页面层**: 页面特定样式
- **交互层**: 动画和用户交互

### 3. **命名规范**
- 使用 `mucha-` 前缀表示主题相关样式
- 使用 BEM 命名法：`block__element--modifier`
- 语义化类名，便于理解和维护

## 📋 文件详细说明

### `main.css` - 主入口文件
- 导入所有CSS模块
- 定义全局覆盖样式
- 处理特殊情况（打印、高对比度、深色模式）

### `base.css` - 基础样式
- CSS重置和标准化
- 字体定义和全局样式
- 自定义光标和基础装饰

### `layout.css` - 布局系统
- 水平滚动容器
- 页面和容器结构
- 响应式断点和媒体查询

### `components.css` - 通用组件
- 标题样式（h1, h2, h3等）
- 按钮和表单元素
- 装饰性元素和图案

### `navigation.css` - 导航组件
- 滚动进度指示器
- 项目导航菜单
- 联系方式组件

### `animations.css` - 动画效果
- 页面切换动画
- 悬停和交互效果
- 关键帧动画定义

### `pages/` - 页面专用样式
每个页面的特定样式，包括：
- 页面特有的布局
- 装饰性背景元素
- 页面专用的响应式设计

## 🔧 使用指南

### 添加新样式
1. **确定样式类型**：基础、布局、组件还是页面特定
2. **选择对应文件**：根据功能选择合适的CSS文件
3. **遵循命名规范**：使用一致的命名约定
4. **考虑响应式**：为不同屏幕尺寸添加适配

### 修改现有样式
1. **定位样式文件**：根据功能找到对应的CSS文件
2. **检查依赖关系**：确保修改不会影响其他组件
3. **测试响应式**：验证在不同设备上的表现
4. **更新文档**：如有必要，更新相关说明

### 性能优化
- 使用CSS `@import` 进行模块化加载
- 避免重复样式定义
- 合理使用CSS变量和继承
- 压缩生产环境的CSS文件

## 🎨 Art Nouveau 主题特色

### 色彩方案
- **主色**: `#8B4513` (深棕色)
- **金色**: `#DAA520` (金黄色)
- **辅助色**: `#CD853F` (秘鲁色)

### 装饰元素
- 花卉和植物图案
- 几何装饰符号
- 渐变和阴影效果
- 优雅的曲线设计

### 字体选择
- **标题**: 'Cinzel' - 经典衬线字体
- **正文**: 'Playfair Display' - 优雅可读字体

## 🚀 扩展建议

### 未来改进
1. **CSS变量系统**：定义全局颜色和尺寸变量
2. **组件库**：创建可复用的UI组件库
3. **主题切换**：支持多种颜色主题
4. **性能优化**：CSS压缩和合并策略

### 维护建议
1. **定期审查**：检查未使用的样式
2. **文档更新**：保持文档与代码同步
3. **测试覆盖**：确保在各种设备上正常显示
4. **版本控制**：记录重要的样式变更

---

*这个模块化的CSS架构旨在提供清晰、可维护的样式组织方式，同时保持Art Nouveau主题的优雅和一致性。*
