/* ========== 页面5: 项目展示页面 ========== */

.page-projects .mucha-content-panel {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 350px;
  grid-template-rows: 1fr auto;
  gap: 2rem;
  padding: 1rem 3%;
  position: relative;
  overflow: hidden;
}

.projects-showcase {
  position: relative;
  background:
    radial-gradient(circle at 30% 30%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(205, 133, 63, 0.08) 0%, transparent 50%);
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 12px;
  overflow: hidden;
  cursor: grab;
}

.projects-showcase:active {
  cursor: grabbing;
}

.project-info-panel {
  background:
    linear-gradient(135deg,
      rgba(244, 228, 188, 0.95) 0%,
      rgba(232, 213, 183, 0.9) 100%
    );
  border: 2px solid rgba(139, 69, 19, 0.3);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 30px rgba(139, 69, 19, 0.2),
    inset 0 0 20px rgba(218, 165, 32, 0.1);
  transition: all 0.5s ease;
}

.project-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.project-title {
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #8B4513;
  margin: 0;
  text-shadow:
    2px 2px 4px rgba(139, 69, 19, 0.3),
    0 0 15px rgba(218, 165, 32, 0.2);
  background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.project-description {
  flex: 1;
}

.project-description p {
  font-family: 'Playfair Display', serif;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #8B4513;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

.project-tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin: 1rem 0;
}

.tech-tag {
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: #8B4513;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border: 1px solid rgba(139, 69, 19, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tech-tag:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.3), rgba(205, 133, 63, 0.25));
  border-color: rgba(139, 69, 19, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

.project-links {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.project-link {
  font-family: 'Cinzel', serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  color: white;
  background: linear-gradient(135deg, #8B4513 0%, #CD853F 100%);
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.project-link:hover {
  background: linear-gradient(135deg, #CD853F 0%, #DAA520 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
}

.project-link.secondary {
  background: transparent;
  color: #8B4513;
  border-color: #CD853F;
  box-shadow: none;
}

.project-link.secondary:hover {
  background: linear-gradient(135deg, rgba(205, 133, 63, 0.1), rgba(218, 165, 32, 0.1));
  border-color: #8B4513;
}

.project-display {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 15px;
  padding: 3rem;
  text-align: center;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.project-display:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(205, 133, 63, 0.12));
  border-color: rgba(139, 69, 19, 0.3);
  box-shadow: 0 8px 20px rgba(139, 69, 19, 0.2);
}

.project-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400"><g opacity="0.08"><circle cx="150" cy="200" r="120" fill="none" stroke="%23DAA520" stroke-width="3"/><circle cx="150" cy="200" r="80" fill="none" stroke="%23CD853F" stroke-width="2"/><circle cx="150" cy="200" r="40" fill="none" stroke="%238B4513" stroke-width="1"/><path d="M50,50 Q150,30 250,50 Q150,70 50,50" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M50,350 Q150,330 250,350 Q150,370 50,350" stroke="%23DAA520" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.project-display h2 {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3);
  position: relative;
  z-index: 2;
}

.project-display p {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  color: #CD853F;
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.2);
  position: relative;
  z-index: 2;
}

/* 项目导航在navigation.css中已定义 */

/* 项目展示页面装饰背景 */
.page-projects::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 3%;
  width: 100px;
  height: 130px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 130"><g opacity="0.4"><ellipse cx="50" cy="40" rx="18" ry="25" fill="%23DAA520"/><ellipse cx="50" cy="65" rx="25" ry="18" fill="%23CD853F"/><ellipse cx="50" cy="90" rx="18" ry="25" fill="%23DAA520"/><path d="M20,20 Q50,10 80,20 Q50,30 20,20" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M20,110 Q50,100 80,110 Q50,120 20,110" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.page-projects::after {
  content: '';
  position: absolute;
  bottom: 15%;
  right: 3%;
  width: 100px;
  height: 130px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 130"><g opacity="0.4"><ellipse cx="50" cy="40" rx="20" ry="18" fill="%23CD853F"/><ellipse cx="50" cy="65" rx="18" ry="20" fill="%23DAA520"/><ellipse cx="50" cy="90" rx="20" ry="18" fill="%23CD853F"/><path d="M15,15 Q50,8 85,15 Q50,22 15,15" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M15,115 Q50,108 85,115 Q50,122 15,115" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 项目展示页面响应式设计 */
@media (max-width: 1200px) {
  .page-projects .mucha-content-panel {
    grid-template-columns: 1fr 300px;
    gap: 3rem;
  }
  
  .project-display {
    padding: 2.5rem;
    min-height: 350px;
  }
  
  .project-display h2 {
    font-size: 2.2rem;
  }
  
  .project-display p {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .page-projects .mucha-content-panel {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .project-display {
    padding: 2rem;
    min-height: 300px;
  }
  
  .project-display h2 {
    font-size: 2rem;
  }
  
  .project-display p {
    font-size: 1rem;
  }
  
  .page-projects::before {
    top: 5%;
    left: 2%;
    width: 80px;
    height: 100px;
  }
  
  .page-projects::after {
    bottom: 10%;
    right: 2%;
    width: 80px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .project-showcase {
    gap: 1.5rem;
  }
  
  .project-display {
    padding: 1.5rem;
    min-height: 250px;
  }
  
  .project-display h2 {
    font-size: 1.8rem;
  }
  
  .project-display p {
    font-size: 0.9rem;
  }
  
  .page-projects::before,
  .page-projects::after {
    width: 60px;
    height: 80px;
  }
}
