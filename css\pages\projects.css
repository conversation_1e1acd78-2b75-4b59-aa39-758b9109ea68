/* ========== 页面5: 项目展示页面 ========== */

.page-projects .mucha-content-panel {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 4rem;
  align-items: start;
  padding: 2rem 0;
}

.project-showcase {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.project-display {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 15px;
  padding: 3rem;
  text-align: center;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.project-display:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.15), rgba(205, 133, 63, 0.12));
  border-color: rgba(139, 69, 19, 0.3);
  box-shadow: 0 8px 20px rgba(139, 69, 19, 0.2);
}

.project-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400"><g opacity="0.08"><circle cx="150" cy="200" r="120" fill="none" stroke="%23DAA520" stroke-width="3"/><circle cx="150" cy="200" r="80" fill="none" stroke="%23CD853F" stroke-width="2"/><circle cx="150" cy="200" r="40" fill="none" stroke="%238B4513" stroke-width="1"/><path d="M50,50 Q150,30 250,50 Q150,70 50,50" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M50,350 Q150,330 250,350 Q150,370 50,350" stroke="%23DAA520" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.project-display h2 {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B4513;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3);
  position: relative;
  z-index: 2;
}

.project-display p {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  color: #CD853F;
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.2);
  position: relative;
  z-index: 2;
}

/* 项目导航在navigation.css中已定义 */

/* 项目展示页面装饰背景 */
.page-projects::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 3%;
  width: 100px;
  height: 130px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 130"><g opacity="0.4"><ellipse cx="50" cy="40" rx="18" ry="25" fill="%23DAA520"/><ellipse cx="50" cy="65" rx="25" ry="18" fill="%23CD853F"/><ellipse cx="50" cy="90" rx="18" ry="25" fill="%23DAA520"/><path d="M20,20 Q50,10 80,20 Q50,30 20,20" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M20,110 Q50,100 80,110 Q50,120 20,110" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.page-projects::after {
  content: '';
  position: absolute;
  bottom: 15%;
  right: 3%;
  width: 100px;
  height: 130px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 130"><g opacity="0.4"><ellipse cx="50" cy="40" rx="20" ry="18" fill="%23CD853F"/><ellipse cx="50" cy="65" rx="18" ry="20" fill="%23DAA520"/><ellipse cx="50" cy="90" rx="20" ry="18" fill="%23CD853F"/><path d="M15,15 Q50,8 85,15 Q50,22 15,15" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M15,115 Q50,108 85,115 Q50,122 15,115" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 项目展示页面响应式设计 */
@media (max-width: 1200px) {
  .page-projects .mucha-content-panel {
    grid-template-columns: 1fr 300px;
    gap: 3rem;
  }
  
  .project-display {
    padding: 2.5rem;
    min-height: 350px;
  }
  
  .project-display h2 {
    font-size: 2.2rem;
  }
  
  .project-display p {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .page-projects .mucha-content-panel {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .project-display {
    padding: 2rem;
    min-height: 300px;
  }
  
  .project-display h2 {
    font-size: 2rem;
  }
  
  .project-display p {
    font-size: 1rem;
  }
  
  .page-projects::before {
    top: 5%;
    left: 2%;
    width: 80px;
    height: 100px;
  }
  
  .page-projects::after {
    bottom: 10%;
    right: 2%;
    width: 80px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .project-showcase {
    gap: 1.5rem;
  }
  
  .project-display {
    padding: 1.5rem;
    min-height: 250px;
  }
  
  .project-display h2 {
    font-size: 1.8rem;
  }
  
  .project-display p {
    font-size: 0.9rem;
  }
  
  .page-projects::before,
  .page-projects::after {
    width: 60px;
    height: 80px;
  }
}
