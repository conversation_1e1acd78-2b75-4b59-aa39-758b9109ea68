/* ========== 页面4: 联系页面 ========== */

.page-contact .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 3rem;
  padding: 2rem 0;
}

.contact-description {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  line-height: 1.8;
  color: #8B4513;
  text-align: center;
  max-width: 700px;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

/* 联系页面装饰背景 */
.page-contact::before {
  content: '';
  position: absolute;
  top: 12%;
  left: 6%;
  width: 140px;
  height: 180px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 140 180"><g opacity="0.5"><ellipse cx="70" cy="55" rx="22" ry="32" fill="%23DAA520"/><ellipse cx="70" cy="90" rx="32" ry="22" fill="%23CD853F"/><ellipse cx="70" cy="125" rx="22" ry="32" fill="%23DAA520"/><path d="M25,25 Q70,12 115,25 Q70,38 25,25" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M25,155 Q70,142 115,155 Q70,168 25,155" stroke="%238B4513" stroke-width="2" fill="none"/><circle cx="35" cy="60" r="3" fill="%23CD853F"/><circle cx="105" cy="60" r="3" fill="%23CD853F"/><circle cx="35" cy="120" r="3" fill="%23DAA520"/><circle cx="105" cy="120" r="3" fill="%23DAA520"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.page-contact::after {
  content: '';
  position: absolute;
  bottom: 20%;
  right: 6%;
  width: 150px;
  height: 210px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 210"><g opacity="0.6"><ellipse cx="75" cy="60" rx="20" ry="30" fill="%23DAA520"/><ellipse cx="75" cy="105" rx="30" ry="20" fill="%23CD853F"/><ellipse cx="75" cy="150" rx="20" ry="30" fill="%23DAA520"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 联系表单样式（如果需要） */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 500px;
  width: 100%;
  margin-top: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-family: 'Cinzel', serif;
  font-size: 1rem;
  font-weight: 500;
  color: #8B4513;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

.form-group input,
.form-group textarea {
  padding: 1rem;
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.05), rgba(205, 133, 63, 0.03));
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #8B4513;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: rgba(139, 69, 19, 0.4);
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #DAA520, #CD853F);
  border: none;
  border-radius: 8px;
  color: white;
  font-family: 'Cinzel', serif;
  font-size: 1.1rem;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
}

.submit-button:hover {
  background: linear-gradient(135deg, #CD853F, #DAA520);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(218, 165, 32, 0.4);
}

/* 联系页面响应式设计 */
@media (max-width: 1200px) {
  .contact-description {
    font-size: 1.1rem;
    max-width: 600px;
  }
  
  .page-contact::before {
    width: 120px;
    height: 150px;
  }
  
  .page-contact::after {
    width: 130px;
    height: 180px;
  }
}

@media (max-width: 768px) {
  .page-contact .mucha-content-panel {
    gap: 2rem;
  }
  
  .contact-description {
    font-size: 1rem;
    max-width: 500px;
  }
  
  .contact-form {
    max-width: 400px;
  }
  
  .page-contact::before {
    top: 8%;
    left: 3%;
    width: 100px;
    height: 120px;
  }
  
  .page-contact::after {
    bottom: 15%;
    right: 3%;
    width: 110px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .page-contact .mucha-content-panel {
    gap: 1.5rem;
  }
  
  .contact-description {
    font-size: 0.9rem;
    max-width: 350px;
  }
  
  .contact-form {
    max-width: 300px;
  }
  
  .form-group input,
  .form-group textarea {
    padding: 0.8rem;
    font-size: 0.9rem;
  }
  
  .submit-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
  
  .page-contact::before,
  .page-contact::after {
    width: 80px;
    height: 100px;
  }
}
