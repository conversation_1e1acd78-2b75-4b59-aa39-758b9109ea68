/* ========== 水平滚动系统 ========== */

.horizontal-scroll-container {
  display: flex;
  width: 500vw; /* 5个页面 */
  height: 100vh;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.page {
  width: 100vw;
  height: 100vh;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  opacity: 1; /* 所有页面始终可见 */
}

/* ========== 慕夏风格容器 ========== */

.mucha-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 60px 80px;
  background: 
    radial-gradient(ellipse at top left, rgba(218, 165, 32, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(205, 133, 63, 0.06) 0%, transparent 50%);
}

.mucha-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
}

.mucha-title {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B4513;
  text-align: center;
  letter-spacing: 3px;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3);
  position: relative;
}

.mucha-ornament {
  width: 120px;
  height: 60px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.mucha-ornament.left {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 60"><g opacity="0.7"><path d="M10,30 Q30,10 50,30 Q70,50 90,30 Q100,20 110,30" stroke="%23DAA520" stroke-width="3" fill="none"/><circle cx="30" cy="30" r="4" fill="%23CD853F"/><circle cx="70" cy="30" r="4" fill="%23CD853F"/><circle cx="90" cy="30" r="3" fill="%23DAA520"/></g></svg>');
  margin-right: 30px;
}

.mucha-ornament.right {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 60"><g opacity="0.7"><path d="M110,30 Q90,10 70,30 Q50,50 30,30 Q20,20 10,30" stroke="%23DAA520" stroke-width="3" fill="none"/><circle cx="90" cy="30" r="4" fill="%23CD853F"/><circle cx="50" cy="30" r="4" fill="%23CD853F"/><circle cx="30" cy="30" r="3" fill="%23DAA520"/></g></svg>');
  margin-left: 30px;
}

.mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.mucha-footer {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.mucha-floral-pattern {
  width: 300px;
  height: 60px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 60"><g opacity="0.5"><path d="M50,30 Q100,10 150,30 Q200,50 250,30" stroke="%23DAA520" stroke-width="2" fill="none"/><circle cx="75" cy="25" r="3" fill="%23CD853F"/><circle cx="125" cy="35" r="3" fill="%23CD853F"/><circle cx="175" cy="25" r="3" fill="%23CD853F"/><circle cx="225" cy="35" r="3" fill="%23CD853F"/><path d="M20,30 Q35,20 50,30" stroke="%23CD853F" stroke-width="2" fill="none"/><path d="M250,30 Q265,20 280,30" stroke="%23CD853F" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
}

/* ========== 响应式布局 ========== */

@media (max-width: 1200px) {
  .mucha-container {
    padding: 40px 60px;
  }
  
  .mucha-title {
    font-size: 2.2rem;
    letter-spacing: 2px;
  }
  
  .mucha-ornament {
    width: 100px;
    height: 50px;
  }
  
  .mucha-ornament.left {
    margin-right: 20px;
  }
  
  .mucha-ornament.right {
    margin-left: 20px;
  }
}

@media (max-width: 768px) {
  .mucha-container {
    padding: 30px 40px;
  }
  
  .mucha-title {
    font-size: 1.8rem;
    letter-spacing: 1px;
  }
  
  .mucha-ornament {
    width: 80px;
    height: 40px;
  }
  
  .mucha-ornament.left {
    margin-right: 15px;
  }
  
  .mucha-ornament.right {
    margin-left: 15px;
  }
  
  .mucha-floral-pattern {
    width: 250px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .mucha-container {
    padding: 20px 30px;
  }
  
  .mucha-title {
    font-size: 1.5rem;
  }
  
  .mucha-ornament {
    width: 60px;
    height: 30px;
  }
  
  .mucha-ornament.left {
    margin-right: 10px;
  }
  
  .mucha-ornament.right {
    margin-left: 10px;
  }
}
