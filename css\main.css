/* ========== Art Nouveau 主样式文件 ========== */
/* 模块化CSS架构 - 按功能分离 */

/* 基础样式 - 重置、字体、光标等 */
@import url('base.css');

/* 布局系统 - 水平滚动、容器、响应式 */
@import url('layout.css');

/* 通用组件 - 标题、按钮、装饰元素 */
@import url('components.css');

/* 导航组件 - 进度指示器、项目导航、联系方式 */
@import url('navigation.css');

/* 页面专用样式 */
@import url('pages/home.css');
@import url('pages/about.css');
@import url('pages/portfolio.css');
@import url('pages/contact.css');
@import url('pages/projects.css');

/* 动画效果 - 过渡、关键帧动画、交互效果 */
@import url('animations.css');

/* ========== 全局覆盖和特殊样式 ========== */

/* 确保所有页面的基础样式一致性 */
.page {
  position: relative;
  z-index: 1;
}

/* 全局文字选择样式 */
::selection {
  background: rgba(218, 165, 32, 0.3);
  color: #8B4513;
}

::-moz-selection {
  background: rgba(218, 165, 32, 0.3);
  color: #8B4513;
}

/* 全局焦点样式 */
*:focus {
  outline: 2px solid rgba(218, 165, 32, 0.6);
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  .mucha-cursor,
  .scroll-progress-indicator,
  .mouse-follower {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .page {
    page-break-after: always;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .mucha-title,
  .mucha-main-title,
  .mucha-heading {
    color: #000000;
    text-shadow: none;
  }
  
  .contact-icon,
  .nav-icon {
    border: 2px solid #000000;
  }
}

/* 深色模式支持（可选） */
@media (prefers-color-scheme: dark) {
  body {
    background: 
      radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(160, 82, 45, 0.12) 0%, transparent 50%),
      linear-gradient(135deg, #2c1810 0%, #3d2317 50%, #4a2c1a 100%);
  }
  
  .mucha-title,
  .mucha-main-title,
  .mucha-heading {
    color: #DAA520;
  }
  
  .mucha-description,
  .intro-text {
    color: #CD853F;
  }
}
