/**
 * 慕夏风格连续滚动控制器
 * 实现平滑的连续滚动效果，内容从右往左流动显示
 */

class ContinuousScrollController {
  constructor() {
    // 滚动位置（单位：vw，0表示第一页，400表示最后一页）
    this.scrollPosition = 0;
    this.targetScrollPosition = 0; // 目标滚动位置
    this.maxScrollPosition = 400; // 5个页面，最大滚动400vw
    this.isScrolling = false;
    this.scrollStep = 15; // 每次滚动移动的距离（vw）
    this.scrollThreshold = 5; // 降低滚轮敏感度阈值，更灵敏

    // 平滑滚动参数
    this.smoothFactor = 0.15; // 平滑因子，调整为更好的平衡
    this.animationId = null;
    this.lastScrollTime = 0;

    // DOM元素
    this.scrollContainer = document.querySelector('.horizontal-scroll-container');
    this.progressBar = document.querySelector('.progress-bar');
    this.progressLabel = document.querySelector('.progress-indicator-label');
    this.pages = document.querySelectorAll('.page');

    // 动画参数
    this.animationDuration = 200; // 减少动画时间，更快响应
    this.easeType = 'cubic-bezier(0.25, 0.1, 0.25, 1)'; // 更平滑的缓动

    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.updateScrollPosition();
    this.updateProgressIndicator();
    this.addSmoothStyles();
    this.startSmoothScrollLoop();
    this.initCustomCursor();
  }

  addSmoothStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .horizontal-scroll-container {
        will-change: transform;
        transform-style: preserve-3d;
        backface-visibility: hidden;
      }

      .page {
        will-change: transform;
        opacity: 1;
        transform-style: preserve-3d;
        backface-visibility: hidden;
      }

      body {
        overflow: hidden;
      }

      .scroll-progress-indicator {
        transition: opacity 0.3s ease;
      }

      .progress-bar {
        transition: height 0.2s ease-out;
      }

      /* 优化渲染性能 */
      * {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    `;
    document.head.appendChild(style);
  }

  // 启动平滑滚动循环
  startSmoothScrollLoop() {
    const animate = () => {
      // 计算当前位置和目标位置的差值
      const diff = this.targetScrollPosition - this.scrollPosition;

      // 如果差值很小，直接设置为目标位置
      if (Math.abs(diff) < 0.01) {
        this.scrollPosition = this.targetScrollPosition;
      } else {
        // 使用线性插值实现平滑过渡
        this.scrollPosition += diff * this.smoothFactor;
      }

      // 更新DOM
      this.updateScrollPosition();
      this.updateProgressIndicator();

      // 继续动画循环
      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  // 初始化自定义鼠标光标
  initCustomCursor() {
    this.cursor = document.querySelector('.mucha-cursor');
    if (!this.cursor) return;

    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;

    // 鼠标移动跟踪
    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    // 平滑跟随鼠标
    const updateCursor = () => {
      const dx = mouseX - cursorX;
      const dy = mouseY - cursorY;

      cursorX += dx * 0.1;
      cursorY += dy * 0.1;

      this.cursor.style.left = cursorX + 'px';
      this.cursor.style.top = cursorY + 'px';

      requestAnimationFrame(updateCursor);
    };
    updateCursor();

    // 鼠标悬停效果
    document.addEventListener('mouseenter', () => {
      this.cursor.classList.add('hover');
    });

    document.addEventListener('mouseleave', () => {
      this.cursor.classList.remove('hover');
    });

    // 滚动时的视觉反馈
    let scrollTimeout;
    let directionTimeout;
    const originalScrollForward = this.scrollForward.bind(this);
    const originalScrollBackward = this.scrollBackward.bind(this);

    this.scrollForward = (amount) => {
      this.cursor.classList.add('scrolling', 'scroll-right');
      this.cursor.classList.remove('scroll-left');

      clearTimeout(scrollTimeout);
      clearTimeout(directionTimeout);

      scrollTimeout = setTimeout(() => {
        this.cursor.classList.remove('scrolling');
      }, 300);

      directionTimeout = setTimeout(() => {
        this.cursor.classList.remove('scroll-right');
      }, 600);

      return originalScrollForward(amount);
    };

    this.scrollBackward = (amount) => {
      this.cursor.classList.add('scrolling', 'scroll-left');
      this.cursor.classList.remove('scroll-right');

      clearTimeout(scrollTimeout);
      clearTimeout(directionTimeout);

      scrollTimeout = setTimeout(() => {
        this.cursor.classList.remove('scrolling');
      }, 300);

      directionTimeout = setTimeout(() => {
        this.cursor.classList.remove('scroll-left');
      }, 600);

      return originalScrollBackward(amount);
    };
  }
  
  setupEventListeners() {
    // 滚轮事件 - 实现连续滚动
    window.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });

    // 键盘事件
    window.addEventListener('keydown', this.handleKeydown.bind(this));

    // 触摸事件 - 支持移动设备
    let touchStartX = 0;
    let touchStartY = 0;

    window.addEventListener('touchstart', (e) => {
      touchStartX = e.changedTouches[0].screenX;
      touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    window.addEventListener('touchmove', (e) => {
      // 阻止垂直滚动
      e.preventDefault();
    }, { passive: false });

    window.addEventListener('touchend', (e) => {
      const touchEndX = e.changedTouches[0].screenX;
      const touchEndY = e.changedTouches[0].screenY;
      const diffX = touchStartX - touchEndX;
      const diffY = Math.abs(touchStartY - touchEndY);

      // 只处理水平滑动
      if (Math.abs(diffX) > 20 && Math.abs(diffX) > diffY) {
        const scrollAmount = Math.min(Math.abs(diffX) / 3, this.scrollStep * 1.5);
        if (diffX > 0) {
          this.scrollForward(scrollAmount);
        } else {
          this.scrollBackward(scrollAmount);
        }
      }
    }, { passive: true });

    // 窗口大小改变时重新计算
    window.addEventListener('resize', () => {
      this.updateScrollPosition();
    });
  }
  
  handleWheel(event) {
    event.preventDefault();

    const deltaY = event.deltaY;
    const deltaX = event.deltaX;
    const totalDelta = Math.abs(deltaY) + Math.abs(deltaX);

    if (totalDelta < this.scrollThreshold) return;

    // 根据滚轮强度调整滚动距离，更精细的控制
    const scrollAmount = Math.min(totalDelta / 8, this.scrollStep);

    if (deltaY > 0 || deltaX > 0) {
      this.scrollForward(scrollAmount);
    } else {
      this.scrollBackward(scrollAmount);
    }
  }

  handleKeydown(event) {
    if (this.isScrolling) return;

    switch (event.key) {
      case 'ArrowRight':
      case ' ':
        event.preventDefault();
        this.scrollForward(this.scrollStep);
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.scrollBackward(this.scrollStep);
        break;
      case 'Home':
        event.preventDefault();
        this.scrollToPosition(0);
        break;
      case 'End':
        event.preventDefault();
        this.scrollToPosition(this.maxScrollPosition);
        break;
      case 'PageDown':
        event.preventDefault();
        this.scrollForward(this.scrollStep * 3);
        break;
      case 'PageUp':
        event.preventDefault();
        this.scrollBackward(this.scrollStep * 3);
        break;
    }
  }
  
  // 向前滚动（向右移动内容，显示更多右侧内容）
  scrollForward(amount = this.scrollStep) {
    const newPosition = Math.min(this.targetScrollPosition + amount, this.maxScrollPosition);
    this.setTargetPosition(newPosition);
  }

  // 向后滚动（向左移动内容，显示更多左侧内容）
  scrollBackward(amount = this.scrollStep) {
    const newPosition = Math.max(this.targetScrollPosition - amount, 0);
    this.setTargetPosition(newPosition);
  }

  // 设置目标滚动位置
  setTargetPosition(position) {
    // 边界检查
    position = Math.max(0, Math.min(position, this.maxScrollPosition));
    this.targetScrollPosition = position;
  }

  // 滚动到指定位置（用于键盘快捷键等）
  scrollToPosition(position) {
    position = Math.max(0, Math.min(position, this.maxScrollPosition));
    this.targetScrollPosition = position;
    this.scrollPosition = position; // 立即跳转
  }

  // 边界反馈效果
  showBoundaryFeedback(direction) {
    const container = this.scrollContainer;
    const feedbackClass = `boundary-feedback-${direction}`;

    container.classList.add(feedbackClass);
    setTimeout(() => {
      container.classList.remove(feedbackClass);
    }, 200);
  }

  // 更新容器的滚动位置
  updateScrollPosition() {
    const translateX = -this.scrollPosition;
    this.scrollContainer.style.transform = `translateX(${translateX}vw)`;
  }

  // 更新进度指示器
  updateProgressIndicator() {
    if (!this.progressBar || !this.progressLabel) return;

    const progress = (this.scrollPosition / this.maxScrollPosition) * 100;
    this.progressBar.style.height = `${progress}%`;
    this.progressLabel.textContent = `${Math.round(progress)}%`;
  }

  // 获取当前页面区域（用于调试或其他功能）
  getCurrentPageRegion() {
    const pageIndex = Math.floor(this.scrollPosition / 100);
    return Math.min(pageIndex + 1, 5); // 返回1-5
  }

  // 获取滚动进度百分比
  getScrollProgress() {
    return (this.scrollPosition / this.maxScrollPosition) * 100;
  }

  // 平滑滚动到指定页面区域
  scrollToPage(pageNumber) {
    if (pageNumber < 1 || pageNumber > 5) return;
    const targetPosition = (pageNumber - 1) * 100;
    this.scrollToPosition(targetPosition);
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  const continuousScroll = new ContinuousScrollController();
  window.continuousScroll = continuousScroll;

  // 添加一些调试信息（可选）
  if (window.location.search.includes('debug')) {
    window.addEventListener('keydown', (e) => {
      if (e.key === 'i') {
        console.log(`Scroll Position: ${continuousScroll.scrollPosition}vw`);
        console.log(`Progress: ${continuousScroll.getScrollProgress().toFixed(1)}%`);
        console.log(`Current Region: Page ${continuousScroll.getCurrentPageRegion()}`);
      }
    });
  }
});

export default ContinuousScrollController;