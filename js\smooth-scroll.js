/**
 * 慕夏风格连续滚动控制器
 * 实现平滑的连续滚动效果，内容从右往左流动显示
 */

class ContinuousScrollController {
  constructor() {
    // 滚动位置（单位：vw，0表示第一页，400表示最后一页）
    this.scrollPosition = 0;
    this.maxScrollPosition = 400; // 5个页面，最大滚动400vw
    this.isScrolling = false;
    this.scrollStep = 20; // 每次滚动移动的距离（vw）
    this.scrollThreshold = 10; // 滚轮敏感度阈值

    // DOM元素
    this.scrollContainer = document.querySelector('.horizontal-scroll-container');
    this.progressBar = document.querySelector('.progress-bar');
    this.progressLabel = document.querySelector('.progress-indicator-label');
    this.pages = document.querySelectorAll('.page');

    // 动画参数
    this.animationDuration = 300;
    this.easeType = 'cubic-bezier(0.25, 0.46, 0.45, 0.94)';

    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.updateScrollPosition();
    this.updateProgressIndicator();
    this.addSmoothStyles();
  }

  addSmoothStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .horizontal-scroll-container {
        transition: transform ${this.animationDuration}ms ${this.easeType};
        will-change: transform;
      }

      .page {
        will-change: transform;
        opacity: 1;
      }

      body {
        overflow: hidden;
      }

      .scroll-progress-indicator {
        transition: opacity 0.3s ease;
      }

      .progress-bar {
        transition: height ${this.animationDuration}ms ${this.easeType};
      }
    `;
    document.head.appendChild(style);
  }
  
  setupEventListeners() {
    // 滚轮事件 - 实现连续滚动
    window.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });

    // 键盘事件
    window.addEventListener('keydown', this.handleKeydown.bind(this));

    // 触摸事件 - 支持移动设备
    let touchStartX = 0;
    let touchStartY = 0;

    window.addEventListener('touchstart', (e) => {
      touchStartX = e.changedTouches[0].screenX;
      touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    window.addEventListener('touchmove', (e) => {
      // 阻止垂直滚动
      e.preventDefault();
    }, { passive: false });

    window.addEventListener('touchend', (e) => {
      const touchEndX = e.changedTouches[0].screenX;
      const touchEndY = e.changedTouches[0].screenY;
      const diffX = touchStartX - touchEndX;
      const diffY = Math.abs(touchStartY - touchEndY);

      // 只处理水平滑动
      if (Math.abs(diffX) > 30 && Math.abs(diffX) > diffY) {
        const scrollAmount = Math.min(Math.abs(diffX) / 5, this.scrollStep * 2);
        if (diffX > 0) {
          this.scrollForward(scrollAmount);
        } else {
          this.scrollBackward(scrollAmount);
        }
      }
    }, { passive: true });

    // 窗口大小改变时重新计算
    window.addEventListener('resize', () => {
      this.updateScrollPosition();
    });
  }
  
  handleWheel(event) {
    event.preventDefault();

    if (this.isScrolling) return;

    const deltaY = event.deltaY;
    const deltaX = event.deltaX;
    const totalDelta = Math.abs(deltaY) + Math.abs(deltaX);

    if (totalDelta < this.scrollThreshold) return;

    // 根据滚轮强度调整滚动距离
    const scrollAmount = Math.min(totalDelta / 10, this.scrollStep);

    if (deltaY > 0 || deltaX > 0) {
      this.scrollForward(scrollAmount);
    } else {
      this.scrollBackward(scrollAmount);
    }
  }

  handleKeydown(event) {
    if (this.isScrolling) return;

    switch (event.key) {
      case 'ArrowRight':
      case ' ':
        event.preventDefault();
        this.scrollForward(this.scrollStep);
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.scrollBackward(this.scrollStep);
        break;
      case 'Home':
        event.preventDefault();
        this.scrollToPosition(0);
        break;
      case 'End':
        event.preventDefault();
        this.scrollToPosition(this.maxScrollPosition);
        break;
      case 'PageDown':
        event.preventDefault();
        this.scrollForward(this.scrollStep * 3);
        break;
      case 'PageUp':
        event.preventDefault();
        this.scrollBackward(this.scrollStep * 3);
        break;
    }
  }
  
  // 向前滚动（向右移动内容，显示更多右侧内容）
  scrollForward(amount = this.scrollStep) {
    const newPosition = Math.min(this.scrollPosition + amount, this.maxScrollPosition);
    this.scrollToPosition(newPosition);
  }

  // 向后滚动（向左移动内容，显示更多左侧内容）
  scrollBackward(amount = this.scrollStep) {
    const newPosition = Math.max(this.scrollPosition - amount, 0);
    this.scrollToPosition(newPosition);
  }

  // 滚动到指定位置
  scrollToPosition(position) {
    if (this.isScrolling) {
      return;
    }

    // 边界检查和边界反弹效果
    const originalPosition = position;
    position = Math.max(0, Math.min(position, this.maxScrollPosition));

    // 如果到达边界，提供视觉反馈
    if (originalPosition !== position) {
      this.showBoundaryFeedback(originalPosition < 0 ? 'left' : 'right');
    }

    // 如果位置没有变化，不执行滚动
    if (Math.abs(position - this.scrollPosition) < 0.1) {
      return;
    }

    this.isScrolling = true;
    this.scrollPosition = position;

    this.updateScrollPosition();
    this.updateProgressIndicator();

    // 动画完成后重置滚动状态
    setTimeout(() => {
      this.isScrolling = false;
    }, this.animationDuration + 50);
  }

  // 边界反馈效果
  showBoundaryFeedback(direction) {
    const container = this.scrollContainer;
    const feedbackClass = `boundary-feedback-${direction}`;

    container.classList.add(feedbackClass);
    setTimeout(() => {
      container.classList.remove(feedbackClass);
    }, 200);
  }

  // 更新容器的滚动位置
  updateScrollPosition() {
    const translateX = -this.scrollPosition;
    this.scrollContainer.style.transform = `translateX(${translateX}vw)`;
  }

  // 更新进度指示器
  updateProgressIndicator() {
    if (!this.progressBar || !this.progressLabel) return;

    const progress = (this.scrollPosition / this.maxScrollPosition) * 100;
    this.progressBar.style.height = `${progress}%`;
    this.progressLabel.textContent = `${Math.round(progress)}%`;
  }

  // 获取当前页面区域（用于调试或其他功能）
  getCurrentPageRegion() {
    const pageIndex = Math.floor(this.scrollPosition / 100);
    return Math.min(pageIndex + 1, 5); // 返回1-5
  }

  // 获取滚动进度百分比
  getScrollProgress() {
    return (this.scrollPosition / this.maxScrollPosition) * 100;
  }

  // 平滑滚动到指定页面区域
  scrollToPage(pageNumber) {
    if (pageNumber < 1 || pageNumber > 5) return;
    const targetPosition = (pageNumber - 1) * 100;
    this.scrollToPosition(targetPosition);
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  const continuousScroll = new ContinuousScrollController();
  window.continuousScroll = continuousScroll;

  // 添加一些调试信息（可选）
  if (window.location.search.includes('debug')) {
    window.addEventListener('keydown', (e) => {
      if (e.key === 'i') {
        console.log(`Scroll Position: ${continuousScroll.scrollPosition}vw`);
        console.log(`Progress: ${continuousScroll.getScrollProgress().toFixed(1)}%`);
        console.log(`Current Region: Page ${continuousScroll.getCurrentPageRegion()}`);
      }
    });
  }
});

export default ContinuousScrollController;