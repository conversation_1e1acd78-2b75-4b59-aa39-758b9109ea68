/**
 * 慕夏风格原创艺术模块
 * 创建动态的花卉螺旋艺术效果
 */

import * as THREE from './three.min.js';

class MuchaArt {
  constructor(container) {
    this.container = container;
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ 
      antialias: true, 
      alpha: true,
      powerPreference: "high-performance"
    });
    this.clock = new THREE.Clock();
    
    this.muchaColors = {
      gold: new THREE.Color('#DAA520'),
      bronze: new THREE.Color('#CD853F'),
      darkBrown: new THREE.Color('#8B4513'),
      lightGold: new THREE.Color('#F4E4BC'),
      mediumGold: new THREE.Color('#E8D5B7'),
      warmBrown: new THREE.Color('#D4B996')
    };
    
    this.artGroups = {
      spirals: new THREE.Group(),
      flowers: new THREE.Group(),
      particles: new THREE.Group()
    };
    
    this.uniforms = {
      uTime: { value: 0 },
      uMouse: { value: new THREE.Vector2() },
      uResolution: { value: new THREE.Vector2() }
    };
    
    this.init();
  }
  
  init() {
    this.setupRenderer();
    this.setupCamera();
    this.createArtwork();
    this.setupEventListeners();
    this.animate();
  }
  
  setupRenderer() {
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.setClearColor(0x000000, 0);
    this.container.appendChild(this.renderer.domElement);
  }
  
  setupCamera() {
    this.camera.position.set(0, 0, 5);
    this.camera.lookAt(0, 0, 0);
  }
  
  createFloralTexture() {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const ctx = canvas.getContext('2d');
    
    // 创建径向渐变背景
    const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
    gradient.addColorStop(0, 'rgba(244, 228, 188, 0.9)');
    gradient.addColorStop(0.5, 'rgba(218, 165, 32, 0.7)');
    gradient.addColorStop(1, 'rgba(139, 69, 19, 0.5)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 512, 512);
    
    // 绘制慕夏风格花卉图案
    for (let i = 0; i < 12; i++) {
      const angle = (i / 12) * Math.PI * 2;
      const radius = 100 + Math.sin(i * 0.5) * 50;
      const x = 256 + Math.cos(angle) * radius;
      const y = 256 + Math.sin(angle) * radius;
      
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(angle + Math.PI / 2);
      
      // 花瓣
      ctx.fillStyle = `rgba(205, 133, 63, ${0.6 + Math.sin(i) * 0.2})`;
      ctx.beginPath();
      ctx.ellipse(0, 0, 20, 8, 0, 0, Math.PI * 2);
      ctx.fill();
      
      // 花瓣装饰线
      ctx.strokeStyle = 'rgba(139, 69, 19, 0.8)';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(-15, 0);
      ctx.lineTo(15, 0);
      ctx.stroke();
      
      ctx.restore();
    }
    
    // 中心装饰
    ctx.fillStyle = 'rgba(139, 69, 19, 0.9)';
    ctx.beginPath();
    ctx.arc(256, 256, 12, 0, Math.PI * 2);
    ctx.fill();
    
    const texture = new CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(2, 2);
    
    return texture;
  }
  
  createSpiralShaders() {
    const vertexShader = `
      varying vec2 vUv;
      varying vec3 vPosition;
      uniform float uTime;
      
      void main() {
        vUv = uv;
        vPosition = position;
        
        vec3 newPosition = position;
        
        // 慕夏风格螺旋变形
        float spiral = atan(position.x, position.z) * 3.0 + length(position.xz) * 2.0;
        float wave = sin(spiral + uTime * 2.0) * 0.2;
        newPosition.y += wave;
        
        // 径向波动
        float radial = length(position.xz);
        newPosition.y += sin(radial * 4.0 + uTime * 1.5) * 0.1;
        
        // 艺术化扭曲
        float twist = sin(position.y * 2.0 + uTime) * 0.1;
        newPosition.x += cos(position.y * 3.0 + uTime) * twist;
        newPosition.z += sin(position.y * 3.0 + uTime) * twist;
        
        gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
      }
    `;
    
    const fragmentShader = `
      varying vec2 vUv;
      varying vec3 vPosition;
      uniform float uTime;
      uniform vec3 uColorA;
      uniform vec3 uColorB;
      uniform vec3 uColorC;
      uniform sampler2D uTexture;
      uniform vec2 uMouse;
      
      void main() {
        vec2 center = vec2(0.5);
        float dist = distance(vUv, center);
        
        // 慕夏风格螺旋图案
        float angle = atan(vUv.y - 0.5, vUv.x - 0.5);
        float spiral = sin(angle * 8.0 + dist * 15.0 + uTime * 3.0) * 0.5 + 0.5;
        
        // 花卉图案
        float floral = sin(dist * 12.0 + uTime * 2.0) * 0.3 + 0.7;
        
        // 鼠标交互效果
        vec2 mouseInfluence = vUv - uMouse;
        float mouseEffect = 1.0 - smoothstep(0.0, 0.3, length(mouseInfluence));
        
        // 颜色混合
        vec3 color1 = mix(uColorA, uColorB, spiral);
        vec3 color2 = mix(uColorB, uColorC, floral);
        vec3 finalColor = mix(color1, color2, sin(uTime * 0.8) * 0.4 + 0.5);
        
        // 纹理叠加
        vec4 texColor = texture2D(uTexture, vUv);
        finalColor = mix(finalColor, texColor.rgb, 0.6);
        
        // 鼠标交互高亮
        finalColor += mouseEffect * 0.3;
        
        // 艺术化透明度
        float alpha = (1.0 - smoothstep(0.2, 0.7, dist)) * (spiral * 0.6 + 0.4);
        alpha *= (1.0 + mouseEffect * 0.5);
        
        gl_FragColor = vec4(finalColor, alpha * 0.85);
      }
    `;
    
    return { vertexShader, fragmentShader };
  }
  
  createArtwork() {
    const floralTexture = this.createFloralTexture();
    const { vertexShader, fragmentShader } = this.createSpiralShaders();
    
    // 创建多层螺旋艺术
    for (let i = 0; i < 7; i++) {
      const material = new THREE.ShaderMaterial({
        uniforms: {
          ...this.uniforms,
          uColorA: { value: this.muchaColors.lightGold.clone().lerp(this.muchaColors.gold, i / 6) },
          uColorB: { value: this.muchaColors.gold.clone().lerp(this.muchaColors.bronze, i / 6) },
          uColorC: { value: this.muchaColors.bronze.clone().lerp(this.muchaColors.darkBrown, i / 6) },
          uTexture: { value: floralTexture }
        },
        vertexShader,
        fragmentShader,
        transparent: true,
        side: THREE.DoubleSide,
        blending: THREE.AdditiveBlending
      });
      
      const geometry = new THREE.PlaneGeometry(4, 4, 80, 80);
      const mesh = new THREE.Mesh(geometry, material);
      
      mesh.position.y = (i - 3) * 0.15;
      mesh.rotation.x = -Math.PI / 2 + (i - 3) * 0.1;
      mesh.rotation.z = (i * Math.PI) / 12;
      
      this.artGroups.spirals.add(mesh);
    }
    
    // 创建装饰性花朵
    this.createDecorativeFlowers();
    
    // 创建浮动粒子
    this.createFloatingParticles();
    
    // 添加到场景
    this.scene.add(this.artGroups.spirals);
    this.scene.add(this.artGroups.flowers);
    this.scene.add(this.artGroups.particles);
  }
  
  createDecorativeFlowers() {
    for (let i = 0; i < 15; i++) {
      const flowerGroup = new THREE.Group();
      
      // 花瓣
      for (let j = 0; j < 8; j++) {
        const petalGeometry = new THREE.SphereGeometry(0.08, 8, 8);
        const petalMaterial = new THREE.MeshBasicMaterial({
          color: this.muchaColors.bronze.clone().lerp(this.muchaColors.gold, Math.random()),
          transparent: true,
          opacity: 0.7
        });
        
        const petal = new THREE.Mesh(petalGeometry, petalMaterial);
        const angle = (j / 8) * Math.PI * 2;
        petal.position.set(
          Math.cos(angle) * 0.2,
          0,
          Math.sin(angle) * 0.2
        );
        petal.scale.set(1, 0.3, 2);
        
        flowerGroup.add(petal);
      }
      
      // 花芯
      const centerGeometry = new THREE.SphereGeometry(0.04, 12, 12);
      const centerMaterial = new THREE.MeshBasicMaterial({ color: this.muchaColors.darkBrown });
      const center = new THREE.Mesh(centerGeometry, centerMaterial);
      flowerGroup.add(center);
      
      // 位置分布
      const angle = (i / 15) * Math.PI * 2;
      const radius = 2.5 + Math.random() * 1.5;
      flowerGroup.position.set(
        Math.cos(angle) * radius,
        (Math.random() - 0.5) * 2,
        Math.sin(angle) * radius
      );
      
      flowerGroup.userData = {
        originalY: flowerGroup.position.y,
        floatSpeed: 0.5 + Math.random() * 1.5,
        rotateSpeed: 0.01 + Math.random() * 0.02
      };
      
      this.artGroups.flowers.add(flowerGroup);
    }
  }
  
  createFloatingParticles() {
    const particleCount = 100; // 增加粒子数量
    
    // 创建粒子系统
    const particleGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount; i++) {
      // 位置
      positions[i * 3] = (Math.random() - 0.5) * 12;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 8;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 12;
      
      // 颜色（慕夏色彩）
      const colorChoice = Math.random();
      if (colorChoice < 0.4) {
        colors[i * 3] = 0.85; // gold
        colors[i * 3 + 1] = 0.65;
        colors[i * 3 + 2] = 0.13;
      } else if (colorChoice < 0.7) {
        colors[i * 3] = 0.80; // bronze
        colors[i * 3 + 1] = 0.52;
        colors[i * 3 + 2] = 0.25;
      } else {
        colors[i * 3] = 0.54; // dark brown
        colors[i * 3 + 1] = 0.27;
        colors[i * 3 + 2] = 0.07;
      }
      
      // 大小
      sizes[i] = Math.random() * 3 + 1;
      
      // 速度
      velocities[i * 3] = (Math.random() - 0.5) * 0.02;
      velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.01;
      velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.02;
    }
    
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    
    // 粒子材质
    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        uTime: { value: 0 },
        uPixelRatio: { value: Math.min(window.devicePixelRatio, 2) }
      },
      vertexShader: `
        attribute float size;
        uniform float uTime;
        uniform float uPixelRatio;
        varying vec3 vColor;
        
        void main() {
          vColor = color;
          
          vec3 pos = position;
          
          // 慕夏风格的流动效果
          pos.x += sin(uTime * 2.0 + position.y * 0.5) * 0.5;
          pos.y += cos(uTime * 1.5 + position.x * 0.3) * 0.3;
          pos.z += sin(uTime * 1.8 + position.x * 0.4 + position.y * 0.3) * 0.4;
          
          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
          gl_Position = projectionMatrix * mvPosition;
          
          // 根据距离调整大小
          gl_PointSize = size * uPixelRatio * (300.0 / -mvPosition.z);
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        
        void main() {
          // 创建圆形粒子
          vec2 center = gl_PointCoord - 0.5;
          float dist = length(center);
          
          if (dist > 0.5) discard;
          
          // 柔和的边缘
          float alpha = 1.0 - smoothstep(0.3, 0.5, dist);
          
          // 慕夏风格的光晕效果
          float glow = 1.0 - dist * 2.0;
          vec3 finalColor = vColor + glow * 0.3;
          
          gl_FragColor = vec4(finalColor, alpha * 0.8);
        }
      `,
      transparent: true,
      vertexColors: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false
    });
    
    this.particleSystem = new THREE.Points(particleGeometry, particleMaterial);
    this.particleVelocities = velocities;
    this.artGroups.particles.add(this.particleSystem);
  }
  
  setupEventListeners() {
    window.addEventListener('resize', this.onWindowResize.bind(this));
    window.addEventListener('mousemove', this.onMouseMove.bind(this));
    
    // 自定义光标跟随
    document.addEventListener('mousemove', (event) => {
      const cursor = document.querySelector('.mucha-cursor');
      if (cursor) {
        cursor.style.left = event.clientX + 'px';
        cursor.style.top = event.clientY + 'px';
      }
    });
  }
  
  onWindowResize() {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.uniforms.uResolution.value.set(window.innerWidth, window.innerHeight);
  }
  
  onMouseMove(event) {
    this.uniforms.uMouse.value.x = event.clientX / window.innerWidth;
    this.uniforms.uMouse.value.y = 1.0 - (event.clientY / window.innerHeight);
  }
  
  animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    const elapsed = this.clock.getElapsedTime();
    this.uniforms.uTime.value = elapsed;
    
    // 增强的螺旋艺术动画
    this.artGroups.spirals.rotation.y += 0.003;
    this.artGroups.spirals.children.forEach((spiral, index) => {
      spiral.rotation.z += 0.002 * (index + 1);
      
      // 添加微妙的上下浮动
      spiral.position.y += Math.sin(elapsed * 2 + index) * 0.001;
    });
    
    // 装饰花朵动画
    this.artGroups.flowers.children.forEach((flower, index) => {
      const userData = flower.userData;
      flower.position.y = userData.originalY + Math.sin(elapsed * userData.floatSpeed + index) * 0.3;
      flower.rotation.y += userData.rotateSpeed;
    });
    
    // 粒子系统动画
    if (this.particleSystem) {
      this.particleSystem.material.uniforms.uTime.value = elapsed;
      
      const positions = this.particleSystem.geometry.attributes.position.array;
      
      for (let i = 0; i < positions.length; i += 3) {
        // 更新位置
        positions[i] += this.particleVelocities[i];
        positions[i + 1] += this.particleVelocities[i + 1];
        positions[i + 2] += this.particleVelocities[i + 2];
        
        // 边界检测和重置
        if (Math.abs(positions[i]) > 6 || 
            Math.abs(positions[i + 1]) > 4 || 
            Math.abs(positions[i + 2]) > 6) {
          positions[i] = (Math.random() - 0.5) * 12;
          positions[i + 1] = (Math.random() - 0.5) * 8;
          positions[i + 2] = (Math.random() - 0.5) * 12;
        }
      }
      
      this.particleSystem.geometry.attributes.position.needsUpdate = true;
    }
    
    this.renderer.render(this.scene, this.camera);
  }
  
  dispose() {
    this.scene.traverse((object) => {
      if (object.geometry) object.geometry.dispose();
      if (object.material) {
        if (object.material.map) object.material.map.dispose();
        object.material.dispose();
      }
    });
    
    this.renderer.dispose();
    window.removeEventListener('resize', this.onWindowResize.bind(this));
    window.removeEventListener('mousemove', this.onMouseMove.bind(this));
  }
}

// 初始化艺术模块
document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('mucha-canvas-container');
  if (container) {
    const muchaArt = new MuchaArt(container);
    
    // 暴露到全局作用域供页面切换控制
    window.muchaArt = muchaArt;
    
    // 监听页面切换事件，只在第一页显示3D艺术
    const observePageChanges = () => {
      const page1 = document.getElementById('page1');
      if (page1) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // 第一页可见时，确保3D艺术正在运行
              if (muchaArt.renderer.domElement) {
                muchaArt.renderer.domElement.style.opacity = '1';
              }
            } else {
              // 第一页不可见时，隐藏3D艺术以提升性能
              if (muchaArt.renderer.domElement) {
                muchaArt.renderer.domElement.style.opacity = '0.3';
              }
            }
          });
        }, {
          threshold: 0.5
        });
        
        observer.observe(page1);
      }
    };
    
    // 延迟观察页面变化以确保DOM完全加载
    setTimeout(observePageChanges, 1000);
  }
});