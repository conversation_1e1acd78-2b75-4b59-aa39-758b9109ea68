/* ========== 通用组件样式 ========== */

/* 标题样式 */
.mucha-main-title {
  font-family: 'Cinzel', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #8B4513;
  text-align: center;
  margin-bottom: 2rem;
  letter-spacing: 2px;
  text-shadow: 3px 3px 6px rgba(139, 69, 19, 0.3);
  position: relative;
}

.mucha-main-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #DAA520, transparent);
}

.mucha-heading {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 600;
  color: #8B4513;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3);
  margin-bottom: 0.5rem;
}

.mucha-subheading {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 400;
  color: #CD853F;
  font-style: italic;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.3);
  margin-bottom: 1rem;
}

.mucha-description {
  font-family: 'Playfair Display', serif;
  font-size: 1.1rem;
  line-height: 1.8;
  color: #8B4513;
  text-align: center;
  max-width: 600px;
  margin: 0 auto 2rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

/* 装饰性文本 */
.intro-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  line-height: 1.8;
  color: #8B4513;
  margin-bottom: 1.5rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

/* 技能部分 */
.skills-section {
  margin-top: 3rem;
}

.skills-title {
  font-family: 'Cinzel', serif;
  font-size: 1.8rem;
  font-weight: 600;
  color: #8B4513;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.2);
}

.skill-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.skill-item {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border: 2px solid rgba(139, 69, 19, 0.2);
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #8B4513;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
}

.skill-item:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border-color: rgba(139, 69, 19, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

/* 艺术家信息 */
.mucha-artist-info {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 10;
}

.mucha-name {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B4513;
  text-shadow: 3px 3px 6px rgba(139, 69, 19, 0.4);
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
}

.mucha-subtitle {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  font-weight: 400;
  color: #CD853F;
  font-style: italic;
  text-shadow: 2px 2px 4px rgba(205, 133, 63, 0.3);
}

/* 装饰性艺术元素 */
.decorative-art {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.mucha-portrait {
  width: 300px;
  height: 400px;
  background: 
    radial-gradient(ellipse at center, rgba(218, 165, 32, 0.3) 0%, rgba(205, 133, 63, 0.2) 50%, transparent 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400"><g opacity="0.4"><ellipse cx="150" cy="120" rx="60" ry="80" fill="%23DAA520"/><ellipse cx="150" cy="200" rx="80" ry="60" fill="%23CD853F"/><ellipse cx="150" cy="280" rx="60" ry="80" fill="%23DAA520"/><path d="M50,50 Q150,20 250,50 Q150,80 50,50" stroke="%238B4513" stroke-width="3" fill="none"/><path d="M50,350 Q150,320 250,350 Q150,380 50,350" stroke="%238B4513" stroke-width="3" fill="none"/></g></svg>') center/cover no-repeat;
  border-radius: 20px;
  box-shadow: 
    0 10px 30px rgba(139, 69, 19, 0.3),
    inset 0 0 50px rgba(218, 165, 32, 0.1);
  position: relative;
  overflow: hidden;
}

.mucha-portrait::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400"><g opacity="0.3"><path d="M50,50 Q150,20 250,50 Q150,80 50,50" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M50,350 Q150,320 250,350 Q150,380 50,350" stroke="%23DAA520" stroke-width="2" fill="none"/><path d="M20,100 Q50,200 20,300" stroke="%23CD853F" stroke-width="2" fill="none"/><path d="M280,100 Q250,200 280,300" stroke="%23CD853F" stroke-width="2" fill="none"/></g></svg>') center/cover no-repeat;
}

/* 响应式组件样式 */
@media (max-width: 1200px) {
  .mucha-main-title {
    font-size: 2.5rem;
  }
  
  .mucha-heading {
    font-size: 2rem;
  }
  
  .mucha-name {
    font-size: 2rem;
  }
  
  .mucha-portrait {
    width: 250px;
    height: 350px;
  }
}

@media (max-width: 768px) {
  .mucha-main-title {
    font-size: 2rem;
  }
  
  .mucha-heading {
    font-size: 1.8rem;
  }
  
  .mucha-name {
    font-size: 1.8rem;
  }
  
  .skill-items {
    grid-template-columns: 1fr;
  }
  
  .mucha-portrait {
    width: 200px;
    height: 300px;
  }
}

@media (max-width: 480px) {
  .mucha-main-title {
    font-size: 1.5rem;
  }
  
  .mucha-heading {
    font-size: 1.5rem;
  }
  
  .mucha-name {
    font-size: 1.5rem;
  }
  
  .mucha-description {
    font-size: 1rem;
  }
}
