/* ========== 基础样式和重置 ========== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

::-webkit-scrollbar {
  display: none;
}

body {
  font-family: 'Cin<PERSON>', 'Playfair Display', serif;
  background:
    radial-gradient(circle at 25% 25%, rgba(218, 165, 32, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(205, 133, 63, 0.12) 0%, transparent 50%),
    linear-gradient(135deg, #f7f0e1 0%, #f2e8d4 50%, #ede0c7 100%);
  overflow: hidden;
  cursor: none !important;
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 隐藏所有元素的默认鼠标 */
*, *::before, *::after {
  cursor: none !important;
}

/* ========== 慕夏风格箭头光标 ========== */

.mucha-cursor {
  position: fixed;
  width: 20px;
  height: 24px;
  z-index: 2000;
  pointer-events: none;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%238B4513" stroke="%23DAA520" stroke-width="1.5"/></svg>') !important;
  background-size: contain !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  filter: drop-shadow(1px 2px 4px rgba(139, 69, 19, 0.4));
}

/* 清除伪元素 */
.mucha-cursor::before,
.mucha-cursor::after {
  display: none !important;
}

/* 鼠标悬停状态 */
.mucha-cursor.hover {
  transform: translate(-2px, -2px) scale(1.2);
  filter: drop-shadow(2px 3px 8px rgba(139, 69, 19, 0.6));
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%23CD853F" stroke="%23DAA520" stroke-width="1.5"/></svg>') !important;
}

/* 滚动状态 */
.mucha-cursor.scrolling {
  animation: cursor-pulse 0.4s ease-in-out infinite alternate;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 24" fill="none"><path d="M2 2 L18 12 L2 22 L6 12 Z" fill="%23DAA520" stroke="%23F4E4BC" stroke-width="1.5"/></svg>') !important;
}

/* 滚动方向指示 */
.mucha-cursor.scroll-left {
  animation: arrow-left 0.6s ease-in-out;
}

.mucha-cursor.scroll-right {
  animation: arrow-right 0.6s ease-in-out;
}

/* 箭头动画 */
@keyframes cursor-pulse {
  0%, 100% { filter: drop-shadow(1px 2px 4px rgba(139, 69, 19, 0.4)); }
  50% { filter: drop-shadow(2px 3px 8px rgba(139, 69, 19, 0.8)); }
}

@keyframes arrow-left {
  0%, 100% { transform: translate(-2px, -2px); }
  50% { transform: translate(-6px, -2px); }
}

@keyframes arrow-right {
  0%, 100% { transform: translate(-2px, -2px); }
  50% { transform: translate(2px, -2px); }
}

/* ========== 慕夏风格装饰边框 ========== */

.mucha-border-frame {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
  border: 8px solid transparent;
  border-image: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="border-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="%23DAA520" opacity="0.6"/><path d="M5,5 Q10,2 15,5 Q10,8 5,5" stroke="%23CD853F" stroke-width="1" fill="none" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23border-pattern)"/></svg>') 8;
}

.mucha-border-frame::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 2px solid rgba(218, 165, 32, 0.3);
  border-radius: 4px;
}

.mucha-border-frame::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 30px;
  right: 30px;
  bottom: 30px;
  border: 1px solid rgba(205, 133, 63, 0.2);
  border-radius: 2px;
}
