/* ========== 基础样式和重置 ========== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

::-webkit-scrollbar {
  display: none;
}

body {
  font-family: 'Cin<PERSON>', 'Playfair Display', serif;
  background:
    radial-gradient(circle at 25% 25%, rgba(218, 165, 32, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(205, 133, 63, 0.12) 0%, transparent 50%),
    linear-gradient(135deg, #f7f0e1 0%, #f2e8d4 50%, #ede0c7 100%);
  overflow: hidden;
  cursor: none !important;
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 隐藏所有元素的默认鼠标 */
*, *::before, *::after {
  cursor: none !important;
}

/* ========== 慕夏风格自定义光标 ========== */

.mucha-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #DAA520 0%, #CD853F 70%, transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 10000;
  mix-blend-mode: difference;
  transition: transform 0.1s ease-out;
  box-shadow: 
    0 0 10px rgba(218, 165, 32, 0.6),
    0 0 20px rgba(218, 165, 32, 0.4),
    0 0 30px rgba(218, 165, 32, 0.2);
}

.mucha-cursor::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(218, 165, 32, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.mucha-cursor.hover {
  transform: scale(1.5);
}

.mucha-cursor.hover::before {
  width: 60px;
  height: 60px;
  border-color: rgba(218, 165, 32, 0.6);
}

/* ========== 慕夏风格装饰边框 ========== */

.mucha-border-frame {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
  border: 8px solid transparent;
  border-image: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="border-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="%23DAA520" opacity="0.6"/><path d="M5,5 Q10,2 15,5 Q10,8 5,5" stroke="%23CD853F" stroke-width="1" fill="none" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23border-pattern)"/></svg>') 8;
}

.mucha-border-frame::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 2px solid rgba(218, 165, 32, 0.3);
  border-radius: 4px;
}

.mucha-border-frame::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 30px;
  right: 30px;
  bottom: 30px;
  border: 1px solid rgba(205, 133, 63, 0.2);
  border-radius: 2px;
}
