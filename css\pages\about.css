/* ========== 页面2: 关于页面 ========== */

.page-about .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
  padding: 2rem 0;
}

.about-text {
  flex: 1;
  max-width: 600px;
}

.about-text .mucha-main-title {
  text-align: left;
  margin-bottom: 2rem;
}

.about-text .mucha-description {
  text-align: left;
  margin-bottom: 3rem;
}

/* 关于页面装饰背景 */
.page-about::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 5%;
  width: 150px;
  height: 200px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 200"><g opacity="0.3"><ellipse cx="75" cy="60" rx="25" ry="35" fill="%23DAA520"/><ellipse cx="75" cy="100" rx="35" ry="25" fill="%23CD853F"/><ellipse cx="75" cy="140" rx="25" ry="35" fill="%23DAA520"/><path d="M25,25 Q75,10 125,25 Q75,40 25,25" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M25,175 Q75,160 125,175 Q75,190 25,175" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.page-about::after {
  content: '';
  position: absolute;
  bottom: 20%;
  right: 5%;
  width: 150px;
  height: 210px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 210"><g opacity="0.6"><ellipse cx="75" cy="60" rx="20" ry="30" fill="%23DAA520"/><ellipse cx="75" cy="105" rx="30" ry="20" fill="%23CD853F"/><ellipse cx="75" cy="150" rx="20" ry="30" fill="%23DAA520"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 关于页面响应式设计 */
@media (max-width: 1200px) {
  .page-about .mucha-content-panel {
    gap: 3rem;
  }
  
  .page-about::before,
  .page-about::after {
    width: 120px;
    height: 160px;
  }
}

@media (max-width: 768px) {
  .page-about .mucha-content-panel {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }
  
  .about-text .mucha-main-title {
    text-align: center;
  }
  
  .about-text .mucha-description {
    text-align: center;
  }
  
  .page-about::before {
    top: 5%;
    left: 2%;
    width: 100px;
    height: 130px;
  }
  
  .page-about::after {
    bottom: 10%;
    right: 2%;
    width: 100px;
    height: 140px;
  }
}

@media (max-width: 480px) {
  .page-about .mucha-content-panel {
    gap: 1.5rem;
  }
  
  .page-about::before,
  .page-about::after {
    width: 80px;
    height: 100px;
  }
}
