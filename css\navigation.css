/* ========== 滚动进度指示器 ========== */

.scroll-progress-indicator {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.progress-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(139, 69, 19, 0.3);
  border: 2px solid rgba(218, 165, 32, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.progress-dot.active {
  background: #DAA520;
  border-color: #8B4513;
  box-shadow: 0 0 15px rgba(218, 165, 32, 0.6);
  transform: scale(1.3);
}

.progress-dot:hover {
  background: #CD853F;
  border-color: #8B4513;
  transform: scale(1.2);
}

.progress-indicator-label {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(139, 69, 19, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.scroll-progress-indicator:hover .progress-indicator-label {
  opacity: 1;
}

/* ========== 项目导航 ========== */

.project-navigation {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1rem;
  background:
    linear-gradient(90deg,
      transparent 0%,
      rgba(218, 165, 32, 0.1) 50%,
      transparent 100%
    );
  border-radius: 12px;
  border: 1px solid rgba(139, 69, 19, 0.2);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  border: 2px solid transparent;
  min-width: 80px;
}

.nav-item:hover {
  background: rgba(218, 165, 32, 0.15);
  border-color: rgba(139, 69, 19, 0.3);
  transform: translateY(-3px);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border-color: rgba(139, 69, 19, 0.4);
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
}

.nav-icon {
  font-size: 1.8rem;
  transition: transform 0.3s ease;
  color: #8B4513;
  text-shadow: 1px 1px 3px rgba(139, 69, 19, 0.3);
  filter: drop-shadow(0 2px 4px rgba(218, 165, 32, 0.2));
}

.nav-item:hover .nav-icon {
  transform: scale(1.15) rotate(8deg);
  color: #DAA520;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.4);
}

.nav-item.active .nav-icon {
  color: #DAA520;
  text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.4);
  transform: scale(1.1);
}

.nav-item span {
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: #8B4513;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.1);
}

/* ========== 联系方式组件 ========== */

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border-color: rgba(139, 69, 19, 0.4);
  transform: translateX(8px);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.2);
}

.contact-icon {
  font-size: 2.2rem;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #DAA520, #CD853F);
  border-radius: 50%;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.contact-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(218, 165, 32, 0.4);
}

.contact-info h3 {
  font-family: 'Cinzel', serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #8B4513;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.2);
}

.contact-info p {
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #CD853F;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.2);
}

/* ========== 响应式导航 ========== */

@media (max-width: 1200px) {
  .project-navigation {
    gap: 1.5rem;
  }
  
  .nav-item {
    padding: 0.8rem 1.2rem;
    min-width: 100px;
  }
  
  .nav-icon {
    font-size: 1.6rem;
  }
  
  .nav-item span {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .scroll-progress-indicator {
    right: 1rem;
  }
  
  .project-navigation {
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .nav-item {
    padding: 0.6rem 1rem;
    min-width: 80px;
  }
  
  .nav-icon {
    font-size: 1.4rem;
  }
  
  .nav-item span {
    font-size: 0.7rem;
  }
  
  .contact-methods {
    gap: 1.5rem;
  }
  
  .contact-item {
    padding: 1rem;
    gap: 1rem;
  }
  
  .contact-icon {
    font-size: 1.8rem;
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 480px) {
  .project-navigation {
    gap: 0.5rem;
  }
  
  .nav-item {
    padding: 0.5rem 0.8rem;
    min-width: 70px;
  }
  
  .nav-icon {
    font-size: 1.2rem;
  }
  
  .nav-item span {
    font-size: 0.6rem;
  }
  
  .contact-methods {
    gap: 1rem;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }
  
  .contact-icon {
    font-size: 1.6rem;
    width: 2.5rem;
    height: 2.5rem;
  }
}
