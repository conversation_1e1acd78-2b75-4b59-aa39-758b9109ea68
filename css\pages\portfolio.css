/* ========== 页面3: 作品集页面 ========== */

.page-portfolio .mucha-content-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 3rem;
  padding: 2rem 0;
}

.portfolio-showcase {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  max-width: 800px;
  width: 100%;
}

.portfolio-item {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(205, 133, 63, 0.08));
  border: 2px solid rgba(139, 69, 19, 0.2);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.portfolio-item:hover {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.15));
  border-color: rgba(139, 69, 19, 0.4);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
}

.portfolio-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><g opacity="0.1"><circle cx="100" cy="100" r="80" fill="none" stroke="%23DAA520" stroke-width="2"/><circle cx="100" cy="100" r="40" fill="none" stroke="%23CD853F" stroke-width="2"/><path d="M50,100 Q75,75 100,100 Q125,125 150,100" stroke="%238B4513" stroke-width="1" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.portfolio-item h3 {
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B4513;
  margin-bottom: 1rem;
  text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.2);
  position: relative;
  z-index: 2;
}

.portfolio-item p {
  font-family: 'Playfair Display', serif;
  font-size: 1rem;
  color: #CD853F;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(205, 133, 63, 0.2);
  position: relative;
  z-index: 2;
}

/* 作品集页面装饰背景 */
.page-portfolio::before {
  content: '';
  position: absolute;
  top: 15%;
  left: 8%;
  width: 120px;
  height: 160px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 160"><g opacity="0.4"><ellipse cx="60" cy="50" rx="20" ry="30" fill="%23DAA520"/><ellipse cx="60" cy="80" rx="30" ry="20" fill="%23CD853F"/><ellipse cx="60" cy="110" rx="20" ry="30" fill="%23DAA520"/><path d="M20,20 Q60,10 100,20 Q60,30 20,20" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M20,140 Q60,130 100,140 Q60,150 20,140" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

.page-portfolio::after {
  content: '';
  position: absolute;
  bottom: 15%;
  right: 8%;
  width: 120px;
  height: 160px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 160"><g opacity="0.4"><ellipse cx="60" cy="50" rx="25" ry="20" fill="%23CD853F"/><ellipse cx="60" cy="80" rx="20" ry="25" fill="%23DAA520"/><ellipse cx="60" cy="110" rx="25" ry="20" fill="%23CD853F"/><path d="M15,15 Q60,5 105,15 Q60,25 15,15" stroke="%238B4513" stroke-width="2" fill="none"/><path d="M15,145 Q60,135 105,145 Q60,155 15,145" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 作品集页面响应式设计 */
@media (max-width: 1200px) {
  .portfolio-showcase {
    gap: 2rem;
    max-width: 700px;
  }
  
  .portfolio-item {
    padding: 1.5rem;
  }
  
  .page-portfolio::before,
  .page-portfolio::after {
    width: 100px;
    height: 130px;
  }
}

@media (max-width: 768px) {
  .page-portfolio .mucha-content-panel {
    gap: 2rem;
  }
  
  .portfolio-showcase {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 500px;
  }
  
  .portfolio-item {
    padding: 1.2rem;
  }
  
  .portfolio-item h3 {
    font-size: 1.3rem;
  }
  
  .portfolio-item p {
    font-size: 0.9rem;
  }
  
  .page-portfolio::before {
    top: 10%;
    left: 5%;
    width: 80px;
    height: 100px;
  }
  
  .page-portfolio::after {
    bottom: 10%;
    right: 5%;
    width: 80px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .page-portfolio .mucha-content-panel {
    gap: 1.5rem;
  }
  
  .portfolio-showcase {
    max-width: 350px;
  }
  
  .portfolio-item {
    padding: 1rem;
  }
  
  .portfolio-item h3 {
    font-size: 1.1rem;
  }
  
  .portfolio-item p {
    font-size: 0.8rem;
  }
  
  .page-portfolio::before,
  .page-portfolio::after {
    width: 60px;
    height: 80px;
  }
}
