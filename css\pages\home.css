/* ========== 页面1: 首页样式 ========== */

#page1 .mucha-art-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 2rem 0;
}

#mucha-canvas-container {
  width: 400px;
  height: 500px;
  position: relative;
  background: 
    radial-gradient(ellipse at center, rgba(218, 165, 32, 0.2) 0%, rgba(205, 133, 63, 0.15) 50%, transparent 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 500"><g opacity="0.6"><ellipse cx="200" cy="150" rx="80" ry="100" fill="%23DAA520"/><ellipse cx="200" cy="250" rx="100" ry="80" fill="%23CD853F"/><ellipse cx="200" cy="350" rx="80" ry="100" fill="%23DAA520"/><path d="M50,50 Q200,20 350,50 Q200,80 50,50" stroke="%238B4513" stroke-width="4" fill="none"/><path d="M50,450 Q200,420 350,450 Q200,480 50,450" stroke="%238B4513" stroke-width="4" fill="none"/></g></svg>') center/cover no-repeat;
  border-radius: 20px;
  box-shadow: 
    0 15px 40px rgba(139, 69, 19, 0.4),
    inset 0 0 60px rgba(218, 165, 32, 0.1);
  z-index: 5;
}

#mucha-canvas-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 440 540"><g opacity="0.3"><path d="M20,20 Q220,0 420,20 Q220,40 20,20" stroke="%23DAA520" stroke-width="3" fill="none"/><path d="M20,520 Q220,500 420,520 Q220,540 20,520" stroke="%23DAA520" stroke-width="3" fill="none"/><path d="M20,50 Q40,270 20,490" stroke="%23CD853F" stroke-width="3" fill="none"/><path d="M420,50 Q400,270 420,490" stroke="%23CD853F" stroke-width="3" fill="none"/></g></svg>') center/cover no-repeat;
  z-index: -1;
}

.mucha-text-left {
  position: absolute;
  left: -150px;
  top: 50%;
  transform: translateY(-50%);
  text-align: right;
  z-index: 10;
}

.mucha-text-right {
  position: absolute;
  right: -150px;
  top: 50%;
  transform: translateY(-50%);
  text-align: left;
  z-index: 10;
}

.mucha-text-left .mucha-heading,
.mucha-text-right .mucha-heading {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  letter-spacing: 3px;
}

.mucha-text-left .mucha-subheading,
.mucha-text-right .mucha-subheading {
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

/* 首页特殊装饰 */
#page1 .mucha-art-panel::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  width: 100px;
  height: 100px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g opacity="0.4"><circle cx="50" cy="50" r="30" fill="none" stroke="%23DAA520" stroke-width="3"/><circle cx="50" cy="50" r="15" fill="%23CD853F"/><path d="M20,50 Q35,35 50,50 Q65,65 80,50" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

#page1 .mucha-art-panel::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: 
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g opacity="0.4"><circle cx="50" cy="50" r="30" fill="none" stroke="%23CD853F" stroke-width="3"/><circle cx="50" cy="50" r="15" fill="%23DAA520"/><path d="M20,50 Q35,35 50,50 Q65,65 80,50" stroke="%238B4513" stroke-width="2" fill="none"/></g></svg>') center/contain no-repeat;
  z-index: 1;
}

/* 首页响应式设计 */
@media (max-width: 1200px) {
  #mucha-canvas-container {
    width: 350px;
    height: 450px;
  }
  
  .mucha-text-left {
    left: -120px;
  }
  
  .mucha-text-right {
    right: -120px;
  }
  
  .mucha-text-left .mucha-heading,
  .mucha-text-right .mucha-heading {
    font-size: 2.5rem;
  }
  
  .mucha-text-left .mucha-subheading,
  .mucha-text-right .mucha-subheading {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  #page1 .mucha-art-panel {
    flex-direction: column;
    gap: 2rem;
  }
  
  #mucha-canvas-container {
    width: 300px;
    height: 400px;
  }
  
  .mucha-text-left,
  .mucha-text-right {
    position: static;
    transform: none;
    text-align: center;
  }
  
  .mucha-text-left .mucha-heading,
  .mucha-text-right .mucha-heading {
    font-size: 2rem;
    margin-bottom: 0.3rem;
  }
  
  .mucha-text-left .mucha-subheading,
  .mucha-text-right .mucha-subheading {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 480px) {
  #mucha-canvas-container {
    width: 250px;
    height: 350px;
  }
  
  .mucha-text-left .mucha-heading,
  .mucha-text-right .mucha-heading {
    font-size: 1.5rem;
  }
  
  .mucha-text-left .mucha-subheading,
  .mucha-text-right .mucha-subheading {
    font-size: 1rem;
  }
  
  .mucha-artist-info {
    bottom: 50px;
  }
  
  .mucha-name {
    font-size: 2rem;
  }
  
  .mucha-subtitle {
    font-size: 1rem;
  }
}
