import {
  scene as page4Scene,
  renderer as page4Renderer,
  addResize,
  resize,
  camera as page4Camera,
  getControls,
} from "./modules/renderer.js";
import {
  Mesh,
  Color,
  PlaneBufferGeometry,
  ShaderMaterial,
  DoubleSide,
  Vector3,
  Vector2,
  Group,
  CylinderGeometry,
  SphereGeometry,
  MeshBasicMaterial,
  RingGeometry,
  AdditiveBlending,
  BackSide,
  FrontSide,
  CanvasTexture,
  RepeatWrapping,
} from "./third_party/three.module.js";

/**
 * 慕夏风格页面4模块 - 原创花卉螺旋艺术
 */
const Page4Module = (function() {
  const scene = page4Scene;
  const renderer = page4Renderer;
  const camera = page4Camera;
  
  // 获取three容器
  const threeContainer = document.getElementById('three-container');
  renderer.domElement.style.visibility = 'hidden';

  // 设置相机位置
  camera.position.set(0, 2, 8);
  camera.lookAt(0, 0, 0);
  const controls = getControls();

  // 配置控制器
  controls.enableDamping = true;
  controls.enableZoom = false;
  controls.dampingFactor = 0.05;
  controls.autoRotate = true;
  controls.autoRotateSpeed = 0.5;
  controls.minPolarAngle = Math.PI / 4;
  controls.maxPolarAngle = (3 * Math.PI) / 4;
  controls.target.set(0, 0, 0);
  controls.update();

  // 创建主场景组
  const mainGroup = new Group();
  scene.add(mainGroup);

  // 慕夏风格调色板
  const muchaColors = {
    gold: '#DAA520',
    bronze: '#CD853F', 
    darkBrown: '#8B4513',
    lightGold: '#F4E4BC',
    mediumGold: '#E8D5B7',
    warmBrown: '#D4B996',
    deepGold: '#B8860B'
  };

  // 创建花卉纹理
  function createFloralTexture() {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const ctx = canvas.getContext('2d');
    
    // 渐变背景
    const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
    gradient.addColorStop(0, 'rgba(244, 228, 188, 0.8)');
    gradient.addColorStop(0.5, 'rgba(218, 165, 32, 0.6)');
    gradient.addColorStop(1, 'rgba(139, 69, 19, 0.4)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 256, 256);
    
    // 绘制花朵图案
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2;
      const x = 128 + Math.cos(angle) * 60;
      const y = 128 + Math.sin(angle) * 60;
      
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(angle);
      
      // 花瓣
      ctx.fillStyle = 'rgba(205, 133, 63, 0.7)';
      ctx.beginPath();
      ctx.ellipse(0, 0, 15, 5, 0, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.restore();
    }
    
    // 中心花蕊
    ctx.fillStyle = 'rgba(139, 69, 19, 0.9)';
    ctx.beginPath();
    ctx.arc(128, 128, 8, 0, Math.PI * 2);
    ctx.fill();
    
    return new CanvasTexture(canvas);
  }

  // 螺旋花卉着色器
  const spiralVertexShader = `
    varying vec2 vUv;
    varying vec3 vPosition;
    uniform float uTime;
    uniform float uWaveIntensity;
    
    void main() {
      vUv = uv;
      vPosition = position;
      
      vec3 newPosition = position;
      
      // 螺旋波动效果
      float spiral = atan(position.x, position.z) + length(position.xz) * 0.5;
      float wave = sin(spiral * 8.0 + uTime * 2.0) * uWaveIntensity;
      newPosition.y += wave * 0.3;
      
      // 径向波动
      float radial = length(position.xz);
      newPosition.y += sin(radial * 4.0 + uTime) * 0.1;
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
    }
  `;

  const spiralFragmentShader = `
    varying vec2 vUv;
    varying vec3 vPosition;
    uniform float uTime;
    uniform vec3 uColorA;
    uniform vec3 uColorB;
    uniform vec3 uColorC;
    uniform sampler2D uTexture;
    
    void main() {
      vec2 center = vec2(0.5, 0.5);
      float dist = distance(vUv, center);
      
      // 螺旋图案
      float angle = atan(vUv.y - 0.5, vUv.x - 0.5);
      float spiral = sin(angle * 8.0 + dist * 20.0 + uTime * 3.0) * 0.5 + 0.5;
      
      // 花卉图案
      float floral = sin(dist * 15.0 + uTime * 2.0) * 0.3 + 0.7;
      
      // 混合颜色
      vec3 color1 = mix(uColorA, uColorB, spiral);
      vec3 color2 = mix(uColorB, uColorC, floral);
      vec3 finalColor = mix(color1, color2, sin(uTime * 0.5) * 0.3 + 0.5);
      
      // 纹理叠加
      vec4 texColor = texture2D(uTexture, vUv);
      finalColor = mix(finalColor, texColor.rgb, 0.4);
      
      // 透明度基于距离中心的远近
      float alpha = 1.0 - smoothstep(0.3, 0.8, dist);
      alpha *= (spiral * 0.5 + 0.5);
      
      gl_FragColor = vec4(finalColor, alpha * 0.8);
    }
  `;

  // 动画变量
  let time = 0;
  let isRendering = false;
  let isInitializing = false;
  let isDestroying = false;

  // 创建花卉材质和几何体
  const floralTexture = createFloralTexture();
  floralTexture.wrapS = RepeatWrapping;
  floralTexture.wrapT = RepeatWrapping;
  floralTexture.repeat.set(2, 2);

  const spiralMaterial = new ShaderMaterial({
    uniforms: {
      uTime: { value: 0 },
      uWaveIntensity: { value: 0.2 },
      uColorA: { value: new Color(muchaColors.lightGold) },
      uColorB: { value: new Color(muchaColors.gold) },
      uColorC: { value: new Color(muchaColors.bronze) },
      uTexture: { value: floralTexture }
    },
    vertexShader: spiralVertexShader,
    fragmentShader: spiralFragmentShader,
    transparent: true,
    side: DoubleSide,
    blending: AdditiveBlending
  });

  // 创建多层螺旋平面
  const spiralLayers = [];
  for (let i = 0; i < 5; i++) {
    const geometry = new PlaneBufferGeometry(6, 6, 64, 64);
    const mesh = new Mesh(geometry, spiralMaterial.clone());
    
    mesh.position.y = i * 0.1 - 0.2;
    mesh.rotation.x = -Math.PI / 2;
    mesh.rotation.z = (i * Math.PI) / 8;
    
    mesh.material.uniforms.uWaveIntensity.value = 0.1 + i * 0.05;
    mesh.material.uniforms.uColorA.value = new Color(muchaColors.lightGold).lerp(new Color(muchaColors.gold), i / 4);
    
    mainGroup.add(mesh);
    spiralLayers.push(mesh);
  }

  // 设置渲染器
  renderer.setClearColor(0x000000, 0);
  renderer.setClearAlpha(0);

  /**
   * 销毁场景
   */
  function destroyScene(duration = 1000) {
    return new Promise((resolve) => {
      if (isInitializing || isDestroying) return resolve();
      
      isDestroying = true;
      renderer.domElement.style.visibility = 'hidden';
      
      renderer.setAnimationLoop(null);
      isRendering = false;
      
      const threeContainer = document.getElementById('three-container');
      if (threeContainer && renderer.domElement.parentNode === threeContainer) {
        threeContainer.removeChild(renderer.domElement);
      }
      
      setTimeout(() => {
        isDestroying = false;
        resolve();
      }, duration);
    });
  }

  /**
   * 初始化场景
   */
  function initializeScene(duration = 1000) {
    return new Promise((resolve) => {
      if (isInitializing || isDestroying) return resolve();
      
      isInitializing = true;
      
      const threeContainer = document.getElementById('three-container');
      if (threeContainer && renderer.domElement.parentNode !== threeContainer) {
        threeContainer.appendChild(renderer.domElement);
        renderer.domElement.style.position = 'absolute';
        renderer.domElement.style.top = '0';
        renderer.domElement.style.left = '0';
        renderer.domElement.style.width = '100%';
        renderer.domElement.style.height = '100%';
      }
      renderer.domElement.style.visibility = 'visible';
      
      if (!isRendering) {
        renderer.setAnimationLoop(render);
      }
      
      setTimeout(() => {
        isInitializing = false;
        resolve();
      }, duration);
    });
  }

  /**
   * 渲染循环
   */
  function render() {
    isRendering = true;
    time += 0.01;
    
    controls.update();
    
    // 更新螺旋层动画
    spiralLayers.forEach((layer, index) => {
      layer.material.uniforms.uTime.value = time + index * 0.2;
      layer.rotation.z += 0.002 * (index + 1);
    });
    
    renderer.render(scene, camera);
  }

  // 返回模块接口
  return {
    init: initializeScene,
    destroy: destroyScene,
    render: render
  };
})();

export default Page4Module;