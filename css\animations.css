/* ========== 高级动画效果 ========== */

.mouse-follower {
  position: fixed;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(218, 165, 32, 0.3) 0%, rgba(205, 133, 63, 0.2) 50%, transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: multiply;
  transition: transform 0.2s ease-out;
}

.mouse-follower.active {
  transform: scale(1.5);
  background: radial-gradient(circle, rgba(218, 165, 32, 0.5) 0%, rgba(205, 133, 63, 0.3) 50%, transparent 100%);
}

/* 页面切换动画 */
.page-transition {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-transition.slide-in {
  transform: translateX(0);
  opacity: 1;
}

.page-transition.slide-out {
  transform: translateX(-100vw);
  opacity: 0.8;
}

/* 元素淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 装饰性动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 应用动画类 */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 20s linear infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 延迟动画 */
.animate-delay-1 {
  animation-delay: 0.2s;
}

.animate-delay-2 {
  animation-delay: 0.4s;
}

.animate-delay-3 {
  animation-delay: 0.6s;
}

.animate-delay-4 {
  animation-delay: 0.8s;
}

/* 悬停动画 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* 文字动画 */
.text-glow {
  text-shadow: 
    0 0 5px rgba(218, 165, 32, 0.5),
    0 0 10px rgba(218, 165, 32, 0.3),
    0 0 15px rgba(218, 165, 32, 0.2);
}

.text-shimmer {
  background: linear-gradient(90deg, #8B4513, #DAA520, #CD853F, #8B4513);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(218, 165, 32, 0.3);
  border-top: 4px solid #DAA520;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 进入视口动画 */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* 粒子效果动画 */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.9;
  }
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #DAA520, transparent);
  border-radius: 50%;
  animation: particle-float 4s ease-in-out infinite;
}

.particle:nth-child(2) {
  animation-delay: 0.5s;
  animation-duration: 3.5s;
}

.particle:nth-child(3) {
  animation-delay: 1s;
  animation-duration: 4.5s;
}

.particle:nth-child(4) {
  animation-delay: 1.5s;
  animation-duration: 3s;
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (max-width: 768px) {
  .mouse-follower {
    display: none;
  }
  
  .animate-fade-in-up,
  .animate-fade-in-left,
  .animate-fade-in-right {
    animation-duration: 0.6s;
  }
  
  .hover-lift:hover {
    transform: translateY(-3px);
  }
}
