# Art Nouveau Space - OhMyKing

慕夏风格数字艺术展示空间，采用新艺术运动美学设计的交互式单页面应用。

## 项目结构

```
├── index.html          # 主页面
├── css/               # 样式文件
│   └── mucha-style.css # 慕夏风格样式
├── js/                # JavaScript文件
│   ├── mucha-art.js   # 慕夏风格3D艺术模块
│   └── three.min.js   # Three.js 3D引擎
├── assets/            # 资源文件
│   └── favicon.ico    # 站点图标
└── images/            # 图片资源 (当前为空)
```

## 启动方式

### 使用 npm (推荐)

1. 确保已安装 Node.js (版本 >= 14.0.0)

2. 安装依赖：
   ```bash
   npm install
   ```

3. 启动开发服务器：
   ```bash
   npm start
   ```
   或者
   ```bash
   npm run dev
   ```

4. 浏览器会自动打开 http://localhost:8000

### 可用的 npm 脚本

- `npm start` - 启动开发服务器并自动打开浏览器
- `npm run dev` - 启动开发服务器并自动打开浏览器 (与start相同)
- `npm run serve` - 启动服务器但不自动打开浏览器
- `npm run build` - 构建项目 (静态站点无需构建)
- `npm test` - 运行测试 (当前无测试)

### 使用 Python (旧方式)

如果您仍想使用Python服务器：

```bash
python server.py
```

然后访问 http://localhost:8000

## 功能特性

- 慕夏艺术风格设计
- Three.js 3D交互式艺术
- 响应式设计
- 动态花卉螺旋效果
- 自定义艺术光标
- 装饰性边框和图案
- 优雅的慕夏色彩搭配

## 技术栈

- HTML5
- CSS3 (自定义慕夏风格)
- JavaScript (ES6+ 模块)
- Three.js (3D艺术渲染)
- Google Fonts (Cinzel & Playfair Display)
- 纯原生实现，无其他依赖

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 艺术特色

- 采用慕夏（Alphonse Mucha）新艺术运动风格
- 金棕色调色板：#DAA520, #CD853F, #8B4513
- 动态3D花卉螺旋艺术效果
- 装饰性文字排版和边框设计
- 沉浸式艺术体验

## 注意事项

- 建议使用桌面浏览器访问以获得最佳艺术体验
- 需要现代浏览器支持WebGL和ES6+特性
- 响应式设计支持移动端浏览

## 开发

项目使用静态文件结构，专注于慕夏风格艺术展示。修改文件后刷新浏览器即可看到更改。

服务器配置了无缓存头部，确保开发时能看到最新更改。

### 自定义修改

- 修改 `css/mucha-style.css` 调整视觉风格
- 修改 `js/mucha-art.js` 调整3D艺术效果
- 所有慕夏色彩定义在CSS和JS中可统一调整
